defmodule Drops.Relation.Plugins.Ecto.Query do
  @moduledoc """
  Plugin for defining custom Ecto queries within relation modules.

  This plugin provides the `defquery` macro that allows defining custom query functions
  that return relation structs with the queryable set to the result of the query block.

  ## Usage

      relation(:users) do
        schema("users", infer: true)

        defquery active() do
          from(u in self(), where: u.active == true)
        end
      end

  The `self()` function within the query block returns the relation module.
  """

  use Drops.Relation.Plugin, imports: [defquery: 2]

  defmacro defquery(call, do: block) do
    {name, args} = Macro.decompose_call(call)

    quote do
      @context update_context(__MODULE__, :query, [unquote(name), unquote(args), unquote(Macro.escape(block))])
    end
  end

  def on(:before_compile, relation, _) do
    queries = context(relation, :queries) || []

    query_functions =
      Enum.map(queries, fn query ->
        generate_query_function(query)
      end)

    quote do
      unquote_splicing(query_functions)
    end
  end

  defp generate_query_function(%{name: name, args: args, block: block}) do
    quote do
      def unquote({name, [], args}) do
        # Import Ecto.Query for the query block
        import Ecto.Query

        # Evaluate the query block to get the queryable
        queryable = unquote(block)

        # Return a new relation struct with the queryable set
        new(queryable, [])
      end
    end
  end
end
