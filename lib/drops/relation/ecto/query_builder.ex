defmodule Drops.Relation.Ecto.QueryBuilder do
  @moduledoc """
  Query builder for evaluating Ecto query blocks within relation contexts.

  This module provides a clean environment for evaluating query blocks with
  Ecto.Query imported and the relation context available.
  """

  import Ecto.Query

  @doc """
  Generates a quoted expression that evaluates a query block with proper imports.

  ## Parameters

  - `relation_module` - The relation module to use as context
  - `block` - The query block to evaluate

  ## Returns

  A quoted expression that when evaluated will return the query result.

  ## Examples

      iex> block = quote do: from(u in relation(), where: u.active == true)
      iex> Drops.Relation.Ecto.QueryBuilder.build_query_ast(MyRelation, block)
      {:__block__, [], [...]}
  """
  def build_query_ast(relation_module, block) do
    quote do
      import Ecto.Query

      # Define relation() function in local scope
      def relation(), do: unquote(relation_module)

      # Evaluate the query block
      unquote(block)
    end
  end
end
