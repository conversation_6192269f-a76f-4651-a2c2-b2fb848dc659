defmodule Drops.Relations.Plugins.Ecto.QueryTest do
  use Drops.RelationCase, async: false

  describe "query/1" do
    relation(:users) do
      schema("users", infer: true)

      defquery active() do
        from(u in relation(), where: u.active == true)
      end
    end

    test "defines a custom query accessible via relation module", %{users: users} do
      users.insert(%{name: "<PERSON>", active: false})
      users.insert(%{name: "<PERSON>", active: true})

      assert [%{name: "<PERSON>"}] = users.active() |> Enum.to_list()
    end

    test "defquery returns a relation struct with custom queryable", %{users: users} do
      users.insert(%{name: "<PERSON>", active: false})
      users.insert(%{name: "<PERSON>", active: true})

      active_relation = users.active()

      # Should return a relation struct
      assert %users{} = active_relation

      # Should have the custom queryable set
      assert active_relation.queryable != users.__schema_module__()

      # Should still be enumerable and return filtered results
      assert [%{name: "<PERSON>"}] = Enum.to_list(active_relation)
    end
  end
end
