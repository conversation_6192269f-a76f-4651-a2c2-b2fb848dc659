defmodule Drops.Relations.Plugins.Ecto.QueryTest do
  use Drops.RelationCase, async: false

  describe "query/1" do
    relation(:users) do
      schema("users", infer: true)

      # TODO: make this work - this should use an internal Drops.Relation.Ecto.QueryBuilder
      #       that evaluates the block and returns relation struct with queryable set to what
      #       the block returns.
      #       self() should simply return __MODULE__ for now, as in the relation module.
      defquery active() do
        from(u in self(), where: u.active == true)
      end
    end

    test "defines a custom query accessible via relation module", %{users: users} do
      users.insert(%{name: "<PERSON>", active: false})
      users.insert(%{name: "<PERSON>", active: true})

      assert [%{name: "<PERSON>"}] = users.active() |> Enum.to_list()
    end
  end
end
